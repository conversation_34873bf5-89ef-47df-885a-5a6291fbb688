import ejs from "ejs";
import path from "path";

// --- SCHEDULE DATA ---
const schedules = {
  "XI RPL 1": {
    className: "XI RPL 1",
    location: "SMK Negeri 2 Singosari",
    generatedDate: "2025-07-07",
    schedule: [
      {
        day: "Senin",
        classes: [
          {
            startTime: "07:00",
            endTime: "08:20",
            subject: "B.Ing",
            teacher: "Supadmo, S.Pd",
          },
          {
            startTime: "08:20",
            endTime: "09:40",
            subject: "PJOK",
            teacher: "<PERSON><PERSON>, S.Pd",
          },
          {
            startTime: "10:00",
            endTime: "11:20",
            subject: "MTK",
            teacher: "<PERSON><PERSON><PERSON>, S.Pd",
          },
          {
            startTime: "13:00",
            endTime: "15:00",
            subject: "BI",
            teacher: "<PERSON><PERSON><PERSON>, S.Pd",
          },
        ],
      },
      {
        day: "Selasa",
        classes: [
          {
            startTime: "07:00",
            endTime: "09:40",
            subject: "<PERSON>K<PERSON> (Mobile)",
            teacher: "<PERSON><PERSON><PERSON><PERSON><PERSON>, S.<PERSON>",
          },
          {
            startTime: "10:00",
            endTime: "13:40",
            subject: "MPKK",
            teacher: "<PERSON>fah <PERSON>antebes <PERSON>dra, S.Pd",
          },
        ],
      },
      {
        day: "Rabu",
        classes: [
          {
            startTime: "07:00",
            endTime: "09:40",
            subject: "MPP",
            teacher: "Ewit Irniyah, S.Pd",
          },
          {
            startTime: "10:00",
            endTime: "11:20",
            subject: "SI",
            teacher: "Fidda Zurika Islamia, S.Pd",
          },
          {
            startTime: "12:20",
            endTime: "13:00",
            subject: "BK",
            teacher: "Roudhotul Husna Yanif, S.Psi",
          },
          {
            startTime: "13:00",
            endTime: "15:00",
            subject: "MPKK",
            teacher: "Zulkifli Abdillah, S.Kom",
          },
        ],
      },
      {
        day: "Kamis",
        classes: [
          {
            startTime: "07:00",
            endTime: "10:40",
            subject: "MPKK",
            teacher: "RR. Henning Gratyanis anggraeni, S.Pd.",
          },
          {
            startTime: "10:40",
            endTime: "13:00",
            subject: "B.Ing",
            teacher: "Supadmo, S.Pd",
          },
          {
            startTime: "13:00",
            endTime: "15:00",
            subject: "PAI",
            teacher: "Rufi'ah, S.Ag",
          },
        ],
      },
      {
        day: "Jumat",
        classes: [
          {
            startTime: "07:00",
            endTime: "08:20",
            subject: "B.Jawa",
            teacher: "Amin Machmudi, S.Pd",
          },
          {
            startTime: "08:20",
            endTime: "10:40",
            subject: "PKN",
            teacher: "Samaodin, SAP",
          },
          {
            startTime: "10:40",
            endTime: "13:40",
            subject: "PKDK",
            teacher: "Aang Noeraries Wahyudipasa, S.Si",
          },
        ],
      },
    ],
  },
  "XI RPL 2": {
    className: "XI RPL 2",
    location: "SMK Negeri 2 Singosari",
    generatedDate: "2025-07-07",
    schedule: [
      {
        day: "Senin",
        classes: [
          {
            startTime: "07:00",
            endTime: "09:00",
            subject: "PAI",
            teacher: "Misbah Abdullah Ohoirot, S.Ag",
          },
          {
            startTime: "09:00",
            endTime: "10:40",
            subject: "B.Ing",
            teacher: "Supadmo, S.Pd",
          },
          {
            startTime: "10:40",
            endTime: "13:40",
            subject: "MPKK",
            teacher: "Zulkifli Abdillah, S.Kom",
          },
          {
            startTime: "13:40",
            endTime: "15:00",
            subject: "PJOK",
            teacher: "Slamet Riyadi, S.Pd",
          },
        ],
      },
      {
        day: "Selasa",
        classes: [
          {
            startTime: "07:00",
            endTime: "10:40",
            subject: "MPKK",
            teacher: "Alifah Diantebes Aindra, S.Pd",
          },
          {
            startTime: "10:40",
            endTime: "13:00",
            subject: "BI",
            teacher: "Hj. Titik Mariyati, S.Pd",
          },
          {
            startTime: "13:00",
            endTime: "14:20",
            subject: "B.Jawa",
            teacher: "Amin Machmudi, S.Pd",
          },
        ],
      },
      {
        day: "Rabu",
        classes: [
          {
            startTime: "07:00",
            endTime: "10:40",
            subject: "MPKK",
            teacher: "RR. Henning Gratyanis anggraeni, S.Pd.",
          },
          {
            startTime: "10:40",
            endTime: "13:00",
            subject: "MTK",
            teacher: "Triana Ardiani, S.Pd",
          },
          {
            startTime: "13:00",
            endTime: "14:20",
            subject: "PKN",
            teacher: "Ida Ayu Suniantari, S.Pd,H",
          },
        ],
      },
      {
        day: "Kamis",
        classes: [
          {
            startTime: "07:00",
            endTime: "08:20",
            subject: "BK",
            teacher: "Roudhotul Husna Yanif, S.Psi",
          },
          {
            startTime: "08:20",
            endTime: "10:40",
            subject: "PKDK",
            teacher: "Supadmo, S.Pd",
          },
          {
            startTime: "10:40",
            endTime: "15:00",
            subject: "MPKK (Mobile)",
            teacher: "Zulkifli Abdillah, S.Kom",
          },
        ],
      },
      {
        day: "Jumat",
        classes: [
          {
            startTime: "08:20",
            endTime: "09:40",
            subject: "SI",
            teacher: "Jefry Yudha Bagus Setyawan, S.Pd",
          },
          {
            startTime: "10:00",
            endTime: "13:00",
            subject: "MPP",
            teacher: "Ewit Irniyah, S.Pd",
          },
          {
            startTime: "13:00",
            endTime: "14:20",
            subject: "B.Ing",
            teacher: "Supadmo, S.Pd",
          },
        ],
      },
    ],
  },
  "XI RPL 3": {
    className: "XI RPL 3",
    location: "SMK Negeri 2 Singosari",
    generatedDate: "2025-07-07",
    schedule: [
      {
        day: "Senin",
        classes: [
          {
            startTime: "07:00",
            endTime: "10:40",
            subject: "MPKK (Mobile)",
            teacher: "Zulkifli Abdillah, S.Kom",
          },
          {
            startTime: "10:40",
            endTime: "13:00",
            subject: "B.Jawa",
            teacher: "Tutik Farida, S.Pd",
          },
          {
            startTime: "13:00",
            endTime: "15:00",
            subject: "BI",
            teacher: "Devi Arveni, S.Pd., Gr.",
          },
        ],
      },
      {
        day: "Selasa",
        classes: [
          {
            startTime: "07:00",
            endTime: "08:20",
            subject: "PJOK",
            teacher: "Slamet Riyadi, S.Pd",
          },
          {
            startTime: "08:20",
            endTime: "09:40",
            subject: "B.Ing",
            teacher: "Krismawandi Nugroho, S.Pd",
          },
          {
            startTime: "10:00",
            endTime: "11:20",
            subject: "SI",
            teacher: "Riza Endar Prasetya, S.Pd",
          },
          {
            startTime: "12:20",
            endTime: "15:00",
            subject: "PKDK",
            teacher: "Ewit Irniyah, S.Pd",
          },
        ],
      },
      {
        day: "Rabu",
        classes: [
          {
            startTime: "07:00",
            endTime: "10:40",
            subject: "MPKK",
            teacher: "Alifah Diantebes Aindra, S.Pd",
          },
          {
            startTime: "12:20",
            endTime: "13:00",
            subject: "BK",
            teacher: "Roudhotul Husna Yanif, S.Psi",
          },
          {
            startTime: "13:00",
            endTime: "15:40",
            subject: "MPP",
            teacher: "Ewit Irniyah, S.Pd",
          },
        ],
      },
      {
        day: "Kamis",
        classes: [
          {
            startTime: "07:00",
            endTime: "09:00",
            subject: "PAI",
            teacher: "Misbah Abdullah Ohoirot, S.Ag",
          },
          {
            startTime: "09:00",
            endTime: "10:40",
            subject: "B.Ing",
            teacher: "Krismawandi Nugroho, S.Pd",
          },
          {
            startTime: "10:40",
            endTime: "15:00",
            subject: "MPKK",
            teacher: "RR. Henning Gratyanis anggraeni, S.Pd.",
          },
        ],
      },
      {
        day: "Jumat",
        classes: [
          {
            startTime: "07:00",
            endTime: "10:40",
            subject: "MPKK",
            teacher: "Zulkifli Abdillah, S.Kom",
          },
          {
            startTime: "10:40",
            endTime: "13:00",
            subject: "MTK",
            teacher: "Pohet Bintoto, S.Pd",
          },
          {
            startTime: "13:00",
            endTime: "15:00",
            subject: "PKN",
            teacher: "Krismawandi Nugroho, S.Pd",
          },
        ],
      },
    ],
  },
};

// --- SERVER LOGIC ---
Bun.serve({
  port: 3000,
  async fetch(req) {
    const url = new URL(req.url);
    const selectedClass = url.searchParams.get("class") || "XI RPL 1";
    const scheduleData = schedules[selectedClass];

    if (!scheduleData) {
      return new Response("Class not found", { status: 404 });
    }

    // Get current time in Jakarta
    const now = new Date(
      new Date().toLocaleString("en-US", { timeZone: "Asia/Jakarta" })
    );
    const days = [
      "Minggu",
      "Senin",
      "Selasa",
      "Rabu",
      "Kamis",
      "Jumat",
      "Sabtu",
    ];
    const currentDayName = days[now.getDay()];
    const currentTime =
      now.getHours().toString().padStart(2, "0") +
      ":" +
      now.getMinutes().toString().padStart(2, "0");

    const currentDaySchedule = scheduleData.schedule.find(
      (day: { day: string }) => day.day === currentDayName
    );

    let currentClass = null;
    if (currentDaySchedule) {
      currentClass = currentDaySchedule.classes.find(
        (cls: { startTime: string; endTime: string }) => {
          return currentTime >= cls.startTime && currentTime < cls.endTime;
        }
      );
    }

    const data = {
      title: "Jadwal Pelajaran",
      scheduleData: scheduleData,
      classNames: Object.keys(schedules),
      selectedClass: selectedClass,
      currentDay: currentDayName,
      currentTime: currentTime,
      currentClass: currentClass,
    };

    const renderedHtml = await ejs.renderFile(
      path.join(import.meta.dir, "views/index.ejs"),
      data
    );

    return new Response(renderedHtml, {
      headers: { "Content-Type": "text/html" },
    });
  },
});

console.log("Server running at http://localhost:3000/");
console.log("Access schedules using query params, e.g.:");
console.log("http://localhost:3000/?class=XI RPL 1");
console.log("http://localhost:3000/?class=XI RPL 2");
console.log("http://localhost:3000/?class=XI RPL 3");
