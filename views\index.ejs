<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><%= title %> - <%= selectedClass %></title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootswatch@5.3.3/dist/darkly/bootstrap.min.css"
      rel="stylesheet"
    />
    <style>
      .nav-pills .nav-link.active {
        background-color: #0d6efd; /* Bootstrap primary color */
        color: white;
      }
      .table-active-custom,
      .table-active-custom > th,
      .table-active-custom > td {
        background-color: rgba(
          13,
          110,
          253,
          0.3
        ) !important; /* Lighter shade for active row */
      }
    </style>
  </head>
  <body>
    <div class="container mt-4">
      <div class="p-5 mb-4 bg-dark-tertiary rounded-3">
        <div class="container-fluid py-5">
          <h1 class="display-5 fw-bold">
            <%= scheduleData.className %> - <%= scheduleData.location %>
          </h1>
          <p class="col-md-8 fs-4">
            Jadwal dihasilkan pada: <%= scheduleData.generatedDate %>
          </p>
        </div>
      </div>

      <!-- Class Selector -->
      <ul class="nav nav-pills mb-4">
        <% classNames.forEach(className => { %>
        <li class="nav-item">
          <a
            class="nav-link <%= (className === selectedClass) ? 'active' : '' %>"
            href="?class=<%= encodeURIComponent(className) %>"
          >
            <%= className %>
          </a>
        </li>
        <% }) %>
      </ul>

      <div class="alert alert-info" role="alert">
        <h4 class="alert-heading">Status Saat Ini (WIB)</h4>
        <p>
          <strong>Hari:</strong> <%= currentDay %> | <strong>Waktu:</strong> <%=
          currentTime %>
        </p>
        <hr />
        <% if (currentClass) { %>
        <p class="mb-0">
          <strong>Pelajaran Saat Ini:</strong> <%= currentClass.subject %> (<%=
          currentClass.startTime %> - <%= currentClass.endTime %>)
        </p>
        <p><strong>Guru:</strong> <%= currentClass.teacher %></p>
        <% } else { %>
        <p class="mb-0">
          <strong>Tidak ada kelas yang sedang berlangsung.</strong>
        </p>
        <% } %>
      </div>

      <h2>Jadwal Mingguan Lengkap</h2>
      <div class="table-responsive">
        <table class="table table-dark table-striped table-bordered">
          <thead class="thead-dark">
            <tr>
              <th scope="col">Hari</th>
              <th scope="col">Waktu</th>
              <th scope="col">Mata Pelajaran</th>
              <th scope="col">Guru</th>
            </tr>
          </thead>
          <tbody>
            <% scheduleData.schedule.forEach(daySchedule => { %> <%
            daySchedule.classes.forEach((classItem, index) => { %> <% const
            isActive = (daySchedule.day === currentDay && currentTime >=
            classItem.startTime && currentTime < classItem.endTime); %>
            <tr class="<%= isActive ? 'table-active-custom' : '' %>">
              <% if (index === 0) { %>
              <td
                rowspan="<%= daySchedule.classes.length %>"
                class="align-middle"
              >
                <%= daySchedule.day %>
              </td>
              <% } %>
              <td><%= classItem.startTime %> - <%= classItem.endTime %></td>
              <td><%= classItem.subject %></td>
              <td><%= classItem.teacher %></td>
            </tr>
            <% }) %> <% }) %>
          </tbody>
        </table>
      </div>
    </div>
  </body>
</html>
